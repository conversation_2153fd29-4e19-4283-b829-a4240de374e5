import { useLocation, useNavigate } from "react-router-dom";
import { Home, User, Code2, FolderOpen, MessageSquare } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { scrollToTopInstant } from "@/components/ScrollToTop";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const navItems = [
  { path: "/", icon: Home, label: "Dashboard" },
  { path: "/about", icon: User, label: "About" },
  { path: "/skills", icon: Code2, label: "Skills" },
  { path: "/projects", icon: FolderOpen, label: "Projects" },
  { path: "/contact", icon: MessageSquare, label: "Contact" },
];

export function FloatingNav() {
  const location = useLocation();
  const navigate = useNavigate();

  // Enhanced navigation function that scrolls to top before navigating
  const handleNavigation = (path: string) => {
    scrollToTopInstant();
    navigate(path);
  };

  return (
    <>
      {/* Desktop Column Navigation - positioned within dedicated column */}
      <div className="hidden lg:block">
        <div className="fixed right-6 top-1/2 -translate-y-1/2 z-50">
          <TooltipProvider>
            <div className="flex flex-col gap-2 rounded-2xl bg-neutral-900/50 p-3 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)]">
              {navItems.map(({ path, icon: Icon, label }) => (
                <Tooltip key={path}>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "w-10 h-10 p-0 rounded-lg transition-all duration-200",
                        location.pathname === path
                          ? "bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg shadow-primary/25" // Active state with primary color
                          : "text-neutral-400 hover:bg-neutral-800 hover:text-white" // Inactive state
                      )}
                      onClick={() => handleNavigation(path)}
                    >
                      <Icon className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="left" className="text-xs font-medium bg-neutral-900/50 backdrop-blur-md shadow-[inset_0_1px_0_0_rgba(255,255,255,0.07)] border-none">
                    <p>{label}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          </TooltipProvider>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="lg:hidden">
        <div className="fixed bottom-0 left-0 right-0 z-30 bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_2px_15px_0_rgba(255,255,255,0.08)] supports-[backdrop-filter]:bg-neutral-900/40">
          <div className="flex items-center justify-around px-2 py-3">
            {navItems.map(({ path, icon: Icon, label }) => (
              <Button
                key={path}
                variant="ghost"
                size="sm"
                className={cn(
                  "flex-col gap-1 h-12 w-12 p-1 rounded-lg transition-all duration-200",
                  location.pathname === path
                    ? "text-primary-foreground bg-primary hover:bg-primary/90 shadow-lg shadow-primary/25"
                    : "text-neutral-400 hover:text-white hover:bg-neutral-800"
                )}
                onClick={() => handleNavigation(path)}
              >
                <Icon className="w-4 h-4" />
                <span className="text-xs font-medium">{label}</span>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
