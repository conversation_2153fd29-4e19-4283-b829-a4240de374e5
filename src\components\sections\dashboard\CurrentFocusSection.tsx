import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>old<PERSON>, <PERSON>O<PERSON>, ExternalLink } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  Too<PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from '@/lib/utils';
import { BaseSectionProps } from './types';
import { renderIcon } from './utils';

// --- 1. DATA RESTRUCTURING ---
// Skills are given unique IDs and serve as the source of truth.
const currentlyLearning = [
  {
    id: "react_patterns",
    name: "Advanced React Patterns",
    icon: "React",
    description: "Exploring composition, custom hooks, and state management for scalable applications."
  },
  {
    id: "ts_practices",
    name: "TypeScript Best Practices",
    icon: "TypeScript",
    description: "Focusing on advanced types, generics, and decorators to write safer, more maintainable code."
  },
  {
    id: "fe_performance",
    name: "Frontend Performance",
    icon: "Performance",
    description: "Mastering code-splitting, lazy loading, and image optimization for faster user experiences."
  },
  {
    id: "ui_ux_design",
    name: "UI/UX Design Principles",
    icon: "Design",
    description: "Applying design fundamentals to create more intuitive and user-friendly interfaces."
  },
];

// Projects now link to skills via the `relatedSkills` array of IDs.
const workingOn = [
  {
    name: "Personal Portfolio Website",
    description: "A performance-optimized showcase of full-stack skills, demonstrating modern React architecture, advanced animations, and accessibility.",
    status: "Active",
    icon: "Portfolio",
    href: "https://github.com/cjjutba/portfolio-v2",
    relatedSkills: ["react_patterns", "fe_performance", "ui_ux_design"]
  },
  {
    name: "E-commerce Platform",
    description: "A complete retail solution built from the ground up, demonstrating end-to-end development from PostgreSQL database design to React frontend implementation.",
    status: "Planning",
    icon: "E-commerce",
    href: "#featured-project",
    relatedSkills: ["react_patterns", "ts_practices", "fe_performance"]
  },
  {
    name: "Task Management App",
    description: "A comprehensive productivity platform featuring real-time collaboration, complex database relationships, and a rich, interactive user interface.",
    status: "Planning",
    icon: "Task",
    href: "#",
    relatedSkills: ["react_patterns", "ts_practices"]
  },
];

// --- Sub-components for Cleanliness ---

const ProjectSelector = ({ project, isSelected, onClick }) => {
  const handleAnchorClick = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    if (href.startsWith("#")) {
      e.preventDefault();
      document.querySelector(href)?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div
      onClick={onClick}
      className="relative group/item p-4 rounded-2xl cursor-pointer transition-colors duration-300 hover:bg-neutral-800/50"
    >
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          {renderIcon(project.icon, "w-5 h-5 text-muted-foreground transition-colors duration-300")}
          <span className="font-semibold text-foreground">{project.name}</span>
        </div>
        <a
          href={project.href}
          onClick={(e) => handleAnchorClick(e, project.href)}
          target={project.href.startsWith("http") ? "_blank" : "_self"}
          rel="noopener noreferrer"
          className="opacity-0 group-hover/item:opacity-100 transition-opacity"
          aria-label={`Link to ${project.name}`}
        >
          <ExternalLink className="w-4 h-4 text-muted-foreground hover:text-foreground" />
        </a>
      </div>
      {isSelected && (
        <motion.div
          layoutId="activeProjectIndicator"
          className="absolute inset-0 bg-purple-500/10 border border-purple-500/50 rounded-2xl -z-10"
          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        />
      )}
    </div>
  );
};

const SkillItem = ({ skill }) => (
  <Tooltip>
    <TooltipTrigger asChild>
      <div className="flex w-full items-center gap-4 p-3 rounded-xl bg-neutral-900/0 border border-transparent">
        {renderIcon(skill.icon, "w-6 h-6 text-blue-400 flex-shrink-0")}
        <span className="font-medium text-foreground">{skill.name}</span>
      </div>
    </TooltipTrigger>
    <TooltipContent side="right" align="center" className="max-w-xs">
      <p>{skill.description}</p>
    </TooltipContent>
  </Tooltip>
);


// --- Main Component ---

export default function CurrentFocusSection({ className }: BaseSectionProps) {
  const [selectedProject, setSelectedProject] = useState(workingOn[0]);

  const relevantSkills = currentlyLearning.filter(skill =>
    selectedProject.relatedSkills.includes(skill.id)
  );

  return (
    <section className={`py-20 lg:py-32 ${className || ''}`} id="current-focus">
      {/* Header Section */}
      <div className="container mx-auto px-4 text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            CURRENT FOCUS
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6 [filter:drop-shadow(0_0_20px_rgba(255,255,255,0.2))]"
        >
          Current{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block pb-3 bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift">
            focus
          </span>
        </motion.h2>
      </div>

      {/* "Focus Hub" Interactive Layout */}
      <TooltipProvider delayDuration={100}>
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            className="grid grid-cols-1 lg:grid-cols-5 gap-10 p-6 sm:p-8 bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] border border-neutral-700/50 rounded-3xl"
          >
            {/* Left Panel: Project Selector */}
            <div className="lg:col-span-2 flex flex-col">
              <div className="mb-6 flex items-center gap-4">
                <div className="w-14 h-14 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl flex items-center justify-center border border-purple-500/20">
                  <Folder className="w-7 h-7 text-purple-500" />
                </div>
                <div>
                  <h3 className="text-xl sm:text-2xl font-bold text-foreground">What I'm Building</h3>
                  <p className="text-muted-foreground text-sm">Select a project to see details.</p>
                </div>
              </div>
              <div className="space-y-2">
                {workingOn.map((project) => (
                  <ProjectSelector
                    key={project.name}
                    project={project}
                    isSelected={selectedProject.name === project.name}
                    onClick={() => setSelectedProject(project)}
                  />
                ))}
              </div>
            </div>

            {/* Right Panel: Dynamic Content */}
            <div className="lg:col-span-3 lg:border-l lg:border-neutral-700/60 lg:pl-10 flex flex-col">
              <div className="mb-6 flex items-center gap-4">
                 <div className="w-14 h-14 bg-gradient-to-br from-blue-500/20 to-teal-500/20 rounded-2xl flex items-center justify-center border border-blue-500/20">
                  <BookOpen className="w-7 h-7 text-blue-500" />
                </div>
                 <div>
                  <h3 className="text-xl sm:text-2xl font-bold text-foreground">Focus & Growth Areas</h3>
                   <p className="text-muted-foreground text-sm">Skills applied to the selected project.</p>
                </div>
              </div>
              <AnimatePresence mode="wait">
                <motion.div
                  key={selectedProject.name} // Key is crucial for AnimatePresence
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                  className="flex flex-col flex-grow"
                >
                  <div className="p-4 bg-neutral-800/30 rounded-xl mb-6">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="text-lg font-bold text-foreground">{selectedProject.name}</h4>
                      <Badge
                        className={cn(
                          "text-xs px-3 py-1 font-semibold rounded-full",
                           selectedProject.status === "Active"
                            ? "bg-green-500/15 text-green-400 border border-green-500/30 shadow-[0_0_8px_0_rgba(34,197,94,0.3)]"
                            : "bg-blue-500/15 text-blue-400 border border-blue-500/30"
                        )}
                      >
                        {selectedProject.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground leading-relaxed">{selectedProject.description}</p>
                  </div>

                  <div className="space-y-3 flex-grow">
                    {relevantSkills.map(skill => (
                      <SkillItem key={skill.id} skill={skill} />
                    ))}
                  </div>

                   <div className="mt-6 pt-4 border-t border-border/20">
                    <p className="text-xs text-muted-foreground italic">
                      My development process is fueled by continuous learning and a drive to apply modern best practices.
                    </p>
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>
          </motion.div>
        </div>
      </TooltipProvider>
    </section>
  );
}