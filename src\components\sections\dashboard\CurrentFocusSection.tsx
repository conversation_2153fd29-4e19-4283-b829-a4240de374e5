import { motion } from 'framer-motion';
import {
  Mail,
  MapPin,
  Globe,
  ExternalLink,
  Monitor,
  Calendar,
  Heart
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { BaseSectionProps } from './types';

// --- BENTO GRID DATA ---
const technologies = [
  { name: "TypeScript", color: "#3178c6" },
  { name: "TailwindCSS", color: "#06b6d4" },
  { name: "Motion", color: "#8b5cf6" },
  { name: "Sanity", color: "#f03e2f" },
  { name: "PostgreSQL", color: "#336791" },
  { name: "MongoDB", color: "#47a248" },
  { name: "Prisma", color: "#2d3748" },
  { name: "pnpm", color: "#f69220" },
  { name: "Bun", color: "#fbf0df" },
  { name: "Git", color: "#f05032" },
  { name: "GitHub", color: "#ffffff" },
  { name: "Vercel", color: "#000000" }
];

const services = [
  {
    title: "Design System & UI",
    subtitle: "Consistency",
    description: "Unified design language and component library"
  },
  {
    title: "API Gateway & Documentation",
    subtitle: "Integration",
    description: "Streamlined API management and comprehensive docs"
  },
  {
    title: "User Onboarding",
    subtitle: "Experience",
    description: "Step-by-step guided user experience flows"
  },
  {
    title: "Payment System",
    subtitle: "Architecture",
    description: "Flexible and secure payment solutions"
  },
  {
    title: "Monitoring & Analytics",
    subtitle: "Infrastructure",
    description: "Provides real-time insights and performance tracking"
  },
  {
    title: "Design System & UI",
    subtitle: "Consistency",
    description: "Unified design language and component library"
  }
];

// --- BENTO GRID COMPONENTS ---

// Hero Card Component
const HeroCard = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.6 }}
    className="relative col-span-1 md:col-span-7 row-span-2 bg-gradient-to-br from-neutral-900/80 to-neutral-800/80 backdrop-blur-lg border border-neutral-700/50 rounded-2xl p-6 overflow-hidden group hover:shadow-2xl transition-all duration-300"
  >
    {/* Collaboration Badge */}
    <div className="flex items-center gap-2 text-sm text-neutral-400 mb-4">
      <Heart className="w-4 h-4" />
      <span>Collaboration</span>
    </div>

    {/* Avatar */}
    <div className="absolute top-6 right-6 w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 p-0.5">
      <div className="w-full h-full rounded-full bg-neutral-900 flex items-center justify-center">
        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-pink-500"></div>
      </div>
    </div>

    {/* Main Content */}
    <div className="mt-16">
      <h3 className="text-xl md:text-2xl font-semibold text-white leading-tight">
        I prioritize client collaboration, fostering open communication
      </h3>
    </div>

    {/* Book Call Button */}
    <div className="absolute bottom-6 left-6">
      <button className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-white text-sm transition-all duration-300">
        <Calendar className="w-4 h-4" />
        Book a call
      </button>
    </div>

    {/* Background Pattern */}
    <div className="absolute inset-0 opacity-5">
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className="absolute inset-0 border border-white/20 rounded-full"
            style={{
              transform: `scale(${1 + i * 0.3})`,
              animationDelay: `${i * 0.5}s`
            }}
          />
        ))}
      </div>
    </div>
  </motion.div>
);

// Tech Stack Card Component
const TechStackCard = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.6, delay: 0.1 }}
    className="col-span-1 md:col-span-5 bg-neutral-900/80 backdrop-blur-lg border border-neutral-700/50 rounded-2xl p-6 hover:shadow-2xl transition-all duration-300"
  >
    <h3 className="text-lg font-semibold text-white mb-6">
      Passionate about cutting-edge technologies
    </h3>

    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
      {technologies.slice(0, 12).map((tech, index) => (
        <motion.div
          key={tech.name}
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.3, delay: index * 0.05 }}
          className="flex items-center gap-2 px-3 py-2 bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg text-sm text-neutral-300 transition-all duration-300 hover:scale-105"
        >
          <div
            className="w-2 h-2 rounded-full"
            style={{ backgroundColor: tech.color }}
          />
          {tech.name}
        </motion.div>
      ))}
    </div>
  </motion.div>
);

// Timezone Card Component
const TimezoneCard = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.6, delay: 0.2 }}
    className="col-span-1 md:col-span-4 row-span-2 bg-gradient-to-br from-blue-500/10 to-purple-500/10 backdrop-blur-lg border border-neutral-700/50 rounded-2xl p-6 hover:shadow-2xl transition-all duration-300"
  >
    <h3 className="text-xl font-semibold text-white mb-2">
      I'm very flexible with time
    </h3>
    <p className="text-blue-400 mb-6">zone communications</p>

    {/* Timezone Badges */}
    <div className="flex gap-4 mb-8">
      {[
        { code: "GB", label: "UK" },
        { code: "IN", label: "India" },
        { code: "US", label: "USA" }
      ].map((zone) => (
        <div key={zone.code} className="px-3 py-1 bg-white/10 border border-white/20 rounded-md text-xs text-neutral-300">
          {zone.code} {zone.label}
        </div>
      ))}
    </div>

    {/* Globe Visualization */}
    <div className="relative h-48 bg-gradient-to-br from-blue-500/20 to-transparent rounded-xl flex items-center justify-center">
      <div className="w-32 h-32 rounded-full border-2 border-blue-500/30 relative">
        <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-500/20 to-transparent">
          {/* Dots pattern */}
          <div className="absolute inset-4 opacity-60">
            {[...Array(40)].map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 bg-blue-400 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 2}s`
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>

    {/* Location Badge */}
    <div className="absolute bottom-6 left-6 flex items-center gap-2 text-sm text-white">
      <MapPin className="w-4 h-4" />
      <span>Remote</span>
      <span className="text-blue-400">India</span>
    </div>
  </motion.div>
);

// Collaboration Card Component
const CollaborationCard = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.6, delay: 0.3 }}
    className="col-span-1 md:col-span-3 bg-neutral-900/80 backdrop-blur-lg border border-neutral-700/50 rounded-2xl p-6 hover:shadow-2xl transition-all duration-300"
  >
    {/* Logo placeholder */}
    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mb-4">
      <span className="text-white font-bold text-xl">AB</span>
    </div>

    <h3 className="text-lg font-semibold text-white mb-2">
      Let's work together
    </h3>
    <p className="text-neutral-400 mb-4">on your next project</p>

    <button className="flex items-center gap-2 px-4 py-2 bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/30 rounded-lg text-white text-sm transition-all duration-300">
      <Mail className="w-4 h-4" />
      <EMAIL>
    </button>
  </motion.div>
);

// Portfolio Preview Card Component
const PortfolioPreviewCard = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.6, delay: 0.4 }}
    className="col-span-1 md:col-span-5 row-span-2 bg-neutral-900/80 backdrop-blur-lg border border-neutral-700/50 rounded-2xl p-6 hover:shadow-2xl transition-all duration-300"
  >
    {/* Browser Chrome */}
    <div className="bg-white/5 rounded-lg p-3 mb-4">
      <div className="flex items-center gap-2 mb-3">
        <div className="flex gap-1.5">
          <div className="w-3 h-3 rounded-full bg-red-500/60"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500/60"></div>
          <div className="w-3 h-3 rounded-full bg-green-500/60"></div>
        </div>
      </div>

      {/* Content Area */}
      <div className="bg-black/30 rounded-md p-4 text-center">
        <p className="text-neutral-400 text-sm mb-3">
          Websites that stand out and make a difference
        </p>
        <div className="flex gap-2 justify-center">
          <button className="px-4 py-1 bg-purple-500 text-white text-xs rounded">
            View Work
          </button>
          <button className="px-4 py-1 bg-neutral-700 text-white text-xs rounded">
            Portfolio
          </button>
        </div>
      </div>
    </div>
  </motion.div>
);

export default function CurrentFocusSection({ className }: BaseSectionProps) {
  return (
    <section className={`py-20 lg:py-32 ${className || ''}`} id="current-focus">
      <div className="container mx-auto px-4">
        {/* Bento Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-12 gap-6 max-w-7xl mx-auto">
          <HeroCard />
          <TechStackCard />
          <TimezoneCard />
          <CollaborationCard />
          <PortfolioPreviewCard />

          {/* Services Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="col-span-1 md:col-span-12 bg-neutral-900/80 backdrop-blur-lg border border-neutral-700/50 rounded-2xl p-6"
          >
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {services.map((service, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-all duration-300"
                >
                  <h4 className="text-sm font-semibold text-white mb-1">{service.title}</h4>
                  <p className="text-xs text-neutral-400 mb-2">{service.subtitle}</p>
                  <p className="text-xs text-neutral-500 leading-relaxed">{service.description}</p>
                </motion.div>
              ))}
            </div>

            {/* Current Project Badge */}
            <div className="mt-6 pt-4 border-t border-neutral-700/50">
              <div className="flex items-center gap-3">
                <Monitor className="w-5 h-5 text-purple-400" />
                <div>
                  <p className="text-xs text-neutral-400">The Inside Scoop</p>
                  <p className="text-sm font-medium text-white">Currently building a SaaS Application</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
