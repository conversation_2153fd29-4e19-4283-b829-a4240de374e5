import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight, Download } from "lucide-react"
import { SiLinkedin, SiGith<PERSON> } from "react-icons/si"
import { userData } from "@/data/personal/userData"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

// It's good practice to keep asset paths as constants
const PROFILE_IMAGE_URL = "/image/profile.png"

export function Sidebar() {
  const [isExpanded, setIsExpanded] = useState(true)

  // Load sidebar state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarState')
    if (savedState === 'collapsed') {
      setIsExpanded(false)
    }
  }, [])

  // Save sidebar state to localStorage and dispatch event
  const toggleSidebar = () => {
    const newState = !isExpanded
    setIsExpanded(newState)
    localStorage.setItem('sidebarState', newState ? 'expanded' : 'collapsed')

    // Dispatch custom event to notify other components
    window.dispatchEvent(new Event('sidebarToggle'))
  }
  return (
    <>
      {/* Desktop Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 z-30 h-screen border-r border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/60 transition-all duration-300 ease-in-out hidden lg:block",
          isExpanded ? "w-80" : "w-20"
        )}
      >
        {/* Background gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-background/90 via-background/95 to-background opacity-60"></div>

        {/* Scrollable container */}
        <div className="relative h-full overflow-y-auto scrollbar-sidebar">

          {/* Main flex container */}
          <div className={cn(
            "flex h-full min-h-screen flex-col transition-all duration-300",
            isExpanded ? "p-10" : "p-4"
          )}>

            {/* Header Section */}
            <div className="flex-grow space-y-6">

              {/* Toggle Button */}
              <div className={cn(
                "flex",
                isExpanded ? "justify-end" : "justify-center"
              )}>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleSidebar}
                  className="w-8 h-8 p-0 rounded-lg hover:bg-muted transition-colors"
                  title={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
                >
                  {isExpanded ? (
                    <ChevronLeft className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </Button>
              </div>

              {/* Avatar Section */}
              <div className="flex justify-center">
                <div className="relative group">
                  <div className="absolute -inset-0.5 bg-gradient-to-br from-primary/30 to-primary/10 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                  <div className="relative">
                    <img
                      src={PROFILE_IMAGE_URL}
                      alt="CJ Jutba"
                      className={cn(
                        "rounded-full object-cover shadow-xl border-2 border-background/50 group-hover:shadow-2xl transition-all duration-300",
                        isExpanded ? "w-32 h-32" : "w-12 h-12"
                      )}
                    />
                    <div className={cn(
                      "absolute bg-emerald-500 rounded-full border-2 border-background shadow-lg",
                      isExpanded ? "bottom-1 right-1 w-6 h-6" : "bottom-0 right-0 w-3 h-3"
                    )}>
                      <div className="w-full h-full bg-emerald-400 rounded-full animate-pulse opacity-75"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Name & Title - Only show when expanded */}
              {isExpanded && (
                <div className="text-center space-y-4">
                  <h1 className="text-display text-5xl text-foreground">
                    {userData.name}
                  </h1>
                  <div className="space-y-3">
                    <p className="text-heading text-xl text-muted-foreground">{userData.title}</p>
                    <div className="h-px w-16 bg-gradient-to-r from-transparent via-primary/60 to-transparent mx-auto"></div>
                  </div>
                </div>
              )}

              {/* Headline - Only show when expanded */}
              {isExpanded && (
                <div className="text-center px-2">
                  <p className="text-caption text-muted-foreground">
                    {userData.bios.short}
                  </p>
                </div>
              )}

            </div>

            {/* Footer with Social Links */}
            <footer className="mt-auto pt-6 border-t border-border/30 space-y-6">
              {isExpanded ? (
                <>
                  {/* Social Links - Expanded */}
                  <div className="flex items-center justify-center gap-6">
                    <a
                      href={userData.social.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground/70 hover:text-[#0077B5] hover:scale-110 transition-all duration-200"
                      title="LinkedIn"
                    >
                      <SiLinkedin className="w-6 h-6" />
                      <span className="sr-only">LinkedIn</span>
                    </a>
                    <a
                      href={userData.social.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground/70 hover:text-foreground hover:scale-110 transition-all duration-200"
                      title="GitHub"
                    >
                      <SiGithub className="w-6 h-6" />
                      <span className="sr-only">GitHub</span>
                    </a>
                  </div>

                  {/* Download Resume - Expanded */}
                  <div className="text-center">
                    <a
                      href="/resume.pdf"
                      download
                      className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-all duration-300 group font-medium hover:underline underline-offset-4"
                    >
                      <Download className="w-4 h-4 group-hover:translate-y-0.5 transition-transform duration-200" />
                      Download Resume
                    </a>
                  </div>
                </>
              ) : (
                <>
                  {/* Social Links - Collapsed */}
                  {/* --- UPDATED --- I increased the gap here from gap-3 to gap-5 for better vertical spacing. */}
                  <div className="flex flex-col items-center gap-8">
                    <a
                      href={userData.social.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground/70 hover:text-[#0077B5] hover:scale-110 transition-all duration-200"
                      title="LinkedIn"
                    >
                      <SiLinkedin className="w-5 h-5" />
                      <span className="sr-only">LinkedIn</span>
                    </a>
                    <a
                      href={userData.social.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground/70 hover:text-foreground hover:scale-110 transition-all duration-200"
                      title="GitHub"
                    >
                      <SiGithub className="w-5 h-5" />
                      <span className="sr-only">GitHub</span>
                    </a>
                    <a
                      href="/resume.pdf"
                      download
                      className="text-muted-foreground/70 hover:text-foreground hover:scale-110 transition-all duration-200"
                      title="Download Resume"
                    >
                      <Download className="w-4 h-4" />
                      <span className="sr-only">Download Resume</span>
                    </a>
                  </div>
                </>
              )}
            </footer>

          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={cn(
        "fixed inset-0 z-40 lg:hidden transition-all duration-300 ease-in-out",
        isExpanded ? "translate-x-0" : "-translate-x-full"
      )}>
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={() => setIsExpanded(false)}
        />

        {/* Mobile Sidebar Content */}
        <div className="relative w-80 h-full bg-background/95 backdrop-blur-xl border-r border-border/50">
          <div className="absolute inset-0 bg-gradient-to-b from-background/90 via-background/95 to-background opacity-60"></div>

          <div className="relative h-full overflow-y-auto scrollbar-sidebar">
            <div className="flex h-full min-h-screen flex-col p-6">

              {/* Mobile Header with Close Button */}
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold">Profile</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(false)}
                  className="w-8 h-8 p-0 rounded-lg hover:bg-muted"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
              </div>

              {/* Mobile Profile Content */}
              <div className="flex-grow space-y-8">
                {/* Avatar Section */}
                <div className="flex justify-center">
                  <div className="relative group">
                    <div className="absolute -inset-0.5 bg-gradient-to-br from-primary/30 to-primary/10 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                    <div className="relative">
                      <img
                        src={PROFILE_IMAGE_URL}
                        alt="CJ Jutba"
                        className="w-32 h-32 rounded-full object-cover shadow-xl border-2 border-background/50 group-hover:shadow-2xl transition-all duration-300"
                      />
                      <div className="absolute bottom-1 right-1 bg-emerald-500 w-6 h-6 rounded-full border-2 border-background shadow-lg">
                        <div className="w-full h-full bg-emerald-400 rounded-full animate-pulse opacity-75"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Name & Title */}
                <div className="text-center space-y-4">
                  <h1 className="text-display text-4xl text-foreground">
                    {userData.name}
                  </h1>
                  <div className="space-y-3">
                    <p className="text-heading text-lg text-muted-foreground">{userData.title}</p>
                    <div className="h-px w-16 bg-gradient-to-r from-transparent via-primary/60 to-transparent mx-auto"></div>
                  </div>
                </div>

                {/* Headline */}
                <div className="text-center px-2">
                  <p className="text-caption text-muted-foreground">
                    {userData.bios.short}
                  </p>
                </div>
              </div>

              {/* Mobile Footer */}
              <footer className="mt-auto pt-6 border-t border-border/30 space-y-6">
                <div className="flex items-center justify-center gap-6">
                  <a
                    href={userData.social.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground/70 hover:text-[#0077B5] hover:scale-110 transition-all duration-200"
                    title="LinkedIn"
                  >
                    <SiLinkedin className="w-6 h-6" />
                    <span className="sr-only">LinkedIn</span>
                  </a>
                  <a
                    href={userData.social.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground/70 hover:text-foreground hover:scale-110 transition-all duration-200"
                    title="GitHub"
                  >
                    <SiGithub className="w-6 h-6" />
                    <span className="sr-only">GitHub</span>
                  </a>
                </div>

                <div className="text-center">
                  <a
                    href="/resume.pdf"
                    download
                    className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-all duration-300 group font-medium hover:underline underline-offset-4"
                  >
                    <Download className="w-4 h-4 group-hover:translate-y-0.5 transition-transform duration-200" />
                    Download Resume
                  </a>
                </div>
              </footer>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Toggle Button - Only show when sidebar is collapsed */}
      {!isExpanded && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(true)}
          className="fixed top-4 left-4 z-50 w-10 h-10 p-0 rounded-lg bg-background/80 backdrop-blur-sm border border-border/50 hover:bg-muted lg:hidden"
          title="Open profile"
        >
          <ChevronRight className="w-4 h-4" />
        </Button>
      )}
    </>
  )
}