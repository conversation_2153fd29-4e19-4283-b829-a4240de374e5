import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Sidebar } from "@/components/Sidebar";
import { FloatingNav } from "@/components/FloatingNav";
import ScrollToTop from "@/components/ScrollToTop";
import SpeedDialFAB from "@/components/SpeedDialFAB";
import Dashboard from "./pages/Dashboard";
import About from "./pages/About";
import Skills from "./pages/Skills";
import Projects from "./pages/Projects";
import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";
import { cn } from "@/lib/utils";

const queryClient = new QueryClient();

const App = () => {
  const [sidebarExpanded, setSidebarExpanded] = useState(true);

  // Listen for sidebar state changes from localStorage
  useEffect(() => {
    const handleStorageChange = () => {
      const savedState = localStorage.getItem('sidebarState');
      setSidebarExpanded(savedState !== 'collapsed');
    };

    // Initial load
    handleStorageChange();

    // Listen for changes
    window.addEventListener('storage', handleStorageChange);

    // Custom event for same-tab changes
    const handleSidebarToggle = () => handleStorageChange();
    window.addEventListener('sidebarToggle', handleSidebarToggle);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sidebarToggle', handleSidebarToggle);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <ScrollToTop />
          <div className="min-h-screen bg-background">
            {/* Three-column layout: Sidebar | Main Content | FloatingNav */}
            <div className="flex min-h-screen">
              {/* Sidebar - Fixed positioned */}
              <Sidebar />

              {/* Main Content Column - Constrained between sidebar and floating nav */}
              <main className={cn(
                "flex-1 min-h-screen pb-16 lg:pb-0 transition-all duration-300 ease-in-out",
                // Account for sidebar width
                sidebarExpanded ? "lg:ml-80" : "lg:ml-20",
                // Account for floating nav width on desktop - provide proper spacing
                "lg:mr-24"
              )}>
                {/* Content wrapper with proper padding to prevent overlap */}
                <div className="px-6 lg:px-12 xl:px-16">
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route path="/about" element={<About />} />
                    <Route path="/skills" element={<Skills />} />
                    <Route path="/projects" element={<Projects />} />
                    <Route path="/contact" element={<Contact />} />
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </div>
              </main>

              {/* FloatingNav - Fixed positioned */}
              <FloatingNav />
            </div>

            {/* Mobile FloatingNav - Bottom navigation */}
            <div className="lg:hidden">
              <FloatingNav />
            </div>

            <SpeedDialFAB />
          </div>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
